import {
  Injectable,
  BadRequestException,
  NotFoundException,
  OnApplicationBootstrap,
  Inject,
  forwardRef,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Not, Repository, MoreThanOrEqual, In } from 'typeorm';
import { War, WarType, WarStatus, WarTarget } from './entity/war.entity';
import { Region, RegionStatus } from '../region/entity/region.entity';
import { User } from '../user/entity/user.entity';
import { CreateWarDto } from './dto/create-war.dto';
import { ParticipateInWarDto } from './dto/participate-in-war.dto';
import { NotificationService } from '../notification/notification.service';
import { NotificationType } from '../notification/entity/notification.entity';
import { SchedulerRegistry } from '@nestjs/schedule';
import { WarAutoService } from './war-auto.service';
import { UserService } from 'src/user/user.service';
import { State } from '../state/entity/state.entity';
import { EnergyService } from 'src/user/energy.service';
import { StateService } from 'src/state/state.service';
import { StateElectionService } from '../state-election/state-election.service';
import { ModuleRef } from '@nestjs/core';
import { calculateHaversineDistance } from '../travel/utils/distance.utils';
import { AvailableTargetDto } from './dto/available-target.dto';

@Injectable()
export class WarService implements OnApplicationBootstrap {
  private readonly WAR_DURATION_MS = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
  private readonly logger = new Logger(WarService.name);
  // private readonly WAR_DURATION_MS = 10 * 60 * 1000; // 10 minutes in milliseconds

  constructor(
    @InjectRepository(War)
    private warRepository: Repository<War>,
    @InjectRepository(Region)
    private regionRepository: Repository<Region>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private notificationService: NotificationService,
    @Inject(forwardRef(() => WarAutoService))
    private warAutoService: WarAutoService,
    private schedulerRegistry: SchedulerRegistry,
    private userService: UserService,
    @Inject(forwardRef(() => EnergyService))
    private energyService: EnergyService,
    private stateService: StateService,
    private stateElectionService: StateElectionService,
    private moduleRef: ModuleRef,
  ) {}

  async findAllWars(): Promise<War[]> {
    return this.warRepository.find({
      relations: {
        attackerRegion: true,
        defenderRegion: true,
        declaredBy: true,
      },
      order: {
        declaredAt: 'DESC', // Add this to sort in descending order
      },
    });
  }

  async findActiveWars(): Promise<War[]> {
    return this.warRepository.find({
      where: [
        { status: WarStatus.PENDING },
        { status: WarStatus.ACTIVE },
      ],
      relations: {
        attackerRegion: true,
        defenderRegion: true,
        declaredBy: true,
      },
    });
  }

  async findWarById(id: string): Promise<War> {
    const war = await this.warRepository.findOne({
      where: { id },
      relations: {
        attackerRegion: true,
        defenderRegion: true,
        declaredBy: true,
        targetRegion: true,
      },
    });

    if (!war) {
      throw new NotFoundException(`War with ID ${id} not found`);
    }

    return war;
  }

  async declareWar(userId: number, createWarDto: CreateWarDto): Promise<War> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['region', 'region.state'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const [attackerRegion, defenderRegion] = await Promise.all([
      this.regionRepository.findOneBy({ id: createWarDto.attackerRegionId }),
      this.regionRepository.findOneBy({ id: createWarDto.defenderRegionId }),
    ]);

    if (!attackerRegion || !defenderRegion) {
      throw new NotFoundException('One or both regions not found');
    }

    // Revolution wars have different authority requirements
    if (createWarDto.warType === WarType.REVOLUTION) {
      await this.validateRevolutionWar(user, defenderRegion);
    } else {
      // Validate if the user has authority to declare war
      const hasAuthority = await this.validateWarAuthority(user);
      if (!hasAuthority) {
        throw new BadRequestException('You do not have authority to declare war');
      }

      // Prevent wars between regions of the same state (except revolution wars)
      await this.validateDifferentStates(attackerRegion, defenderRegion);
    }

    // For ground wars, validate that regions are bordering
    if (createWarDto.warType === WarType.GROUND) {
      const regionsAreBordering = await this.validateBorderingRegions(
        attackerRegion,
        defenderRegion,
      );

      if (!regionsAreBordering) {
        throw new BadRequestException(
          'Cannot declare ground war on a non-bordering region',
        );
      }
    }

    // For sea wars, validate sea access and distance
    if (createWarDto.warType === WarType.SEA) {
      await this.validateSeaWar(attackerRegion, defenderRegion);
    }

    // Determine the damage requirement based on region strength
    // For revolution wars, calculate immediately since we have the region data
    let damageRequirement: number;
    if (createWarDto.warType === WarType.REVOLUTION) {
      damageRequirement = await this.calculateRevolutionDamageRequirement(
        createWarDto.defenderRegionId,
      );
    } else {
      damageRequirement = await this.calculateDamageRequirement(
        createWarDto.defenderRegionId,
        createWarDto.warType,
      );
    }

    const existingWar = await this.warRepository.findOne({
      where: {
        attackerRegion: { id: attackerRegion.id },
        defenderRegion: { id: defenderRegion.id },
        status: Not(In([WarStatus.ENDED, WarStatus.PENDING])),
      },
    });

    if (existingWar) {
      throw new BadRequestException(
        'An active war already exists between these regions',
      );
    }

    // For revolution wars, check if there's already a pending revolution in this region
    if (createWarDto.warType === WarType.REVOLUTION) {
      const existingRevolution = await this.warRepository.findOne({
        where: {
          defenderRegion: { id: createWarDto.defenderRegionId },
          warType: WarType.REVOLUTION,
          status: WarStatus.PENDING,
        },
        relations: ['attackerRegion','defenderRegion']
      });

      if (existingRevolution) {
        // Instead of blocking, automatically add user as participant to existing revolution
        // All revolution logic happens in declareWar method
        return await this.joinExistingRevolution(user, existingRevolution);
      }
    }

    // Determine war status based on war type
    // Revolution wars start in PENDING status until 3 participants commit
    // All other wars start as ACTIVE immediately
    const warStatus = createWarDto.warType === WarType.REVOLUTION
      ? WarStatus.PENDING
      : WarStatus.ACTIVE;

    // Create the war entity
    const newWar = this.warRepository.create({
      warType: createWarDto.warType,
      warTarget: createWarDto.warTarget,
      status: warStatus,
      declaration: createWarDto.declaration,
      attackerRegion,
      defenderRegion,
      declaredBy: user,
      declaredAt: new Date(),
      startedAt: createWarDto.warType === WarType.REVOLUTION ? undefined : new Date(), // Revolution starts when 3 participants commit
      damageRequirement,
      attackerGroundDamage: 0,
      defenderGroundDamage: 0,
      participants: {
        attackers: [],
        defenders: [],
      },
      battleEvents: [],
    });
    const savedWar = await this.warRepository.save(newWar);
    const savedWarWithRelations = await this.warRepository.findOne({
      where: { id: savedWar.id },
      relations: ['attackerRegion', 'defenderRegion'], // add your needed relations
    });
    if(!savedWarWithRelations) {
      throw new BadRequestException('Failed to fetch war with relations');
    }

    // For revolution wars, the declarer (first user) must also pay 500 gold and be added as first participant
    if (createWarDto.warType === WarType.REVOLUTION) {
      // Validate the declarer can participate (same validations as other participants)
      await this.validateRevolutionParticipation(user, savedWarWithRelations);

      // Deduct 500 gold from the declarer
      user.gold -= 500;
      await this.userRepository.save(user);

      // Add declarer as first participant
      savedWar.participants.attackers.push({
        userId: user.id,
        username: user.username,
        damage: 0, // No damage yet, just commitment
        energySpent: 0,
        joinedAt: new Date(),
      });

      // Save the war with the declarer as first participant
      await this.warRepository.save(savedWar);

      console.log(`[Revolution] User ${user.id} declared revolution and became first participant`);
    }

    // Send notifications about war declaration
    await this.sendWarDeclarationNotifications(savedWar);

    // Schedule war end exactly 24 hours from now (only for non-revolution wars)
    // Revolution wars are scheduled when they start (when 3rd participant joins)
    if (createWarDto.warType !== WarType.REVOLUTION) {
      const timeout = global.setTimeout(async () => {
        await this.endWar(savedWar.id);
      }, this.WAR_DURATION_MS);

      // Store the timeout reference
      this.schedulerRegistry.addTimeout(`war_end_${savedWar.id}`, timeout);
    }

    return savedWar;
  }

  private async validateWarAuthority(user: User): Promise<boolean> {
    if (!user.region) {
      return false; // User is not part of a region
    }

    if (!user.region || !user.region.state) {
      return false; // User is not part of a region or region has no state
    }

    const state = await this.stateService.findStateLeader(user.id);

    if (!state) {
      return false; // State not found
    }

    return state.leader.id === user.id; // Check if user is the state leader
  }

  private async validateBorderingRegions(
    attackerRegion: Region,
    defenderRegion: Region,
  ): Promise<boolean> {
    if (!attackerRegion || !attackerRegion.bordersWith) {
      return false;
    }

    return attackerRegion.bordersWith.includes(
      defenderRegion.countryCode,
    );
  }

  private async validateDifferentStates(
    attackerRegion: Region,
    defenderRegion: Region,
  ): Promise<void> {
    // Load state relations if not already loaded
    const attackerWithState = await this.regionRepository.findOne({
      where: { id: attackerRegion.id },
      relations: ['state'],
    });

    const defenderWithState = await this.regionRepository.findOne({
      where: { id: defenderRegion.id },
      relations: ['state'],
    });

    // If either region doesn't exist, this will be caught by other validations
    if (!attackerWithState || !defenderWithState) {
      return;
    }

    // Allow wars if either region is independent (no state)
    if (!attackerWithState.state || !defenderWithState.state) {
      return;
    }

    // Prevent wars between regions of the same state
    if (attackerWithState.state.id === defenderWithState.state.id) {
      throw new BadRequestException(
        `Cannot declare war between regions of the same state (${attackerWithState.state.name}). Use revolution wars for internal conflicts.`,
      );
    }
  }

  private async validateSeaWar(
    attackerRegion: Region,
    defenderRegion: Region,
  ): Promise<void> {
    // Both regions must have sea access
    if (!attackerRegion.seaAccess) {
      throw new BadRequestException(
        'Attacking region must have sea access to declare a sea war',
      );
    }

    if (!defenderRegion.seaAccess) {
      throw new BadRequestException(
        'Defending region must have sea access to be targeted in a sea war',
      );
    }

    // Both regions must have coordinates
    if (!attackerRegion.latitude || !attackerRegion.longitude ||
        !defenderRegion.latitude || !defenderRegion.longitude) {
      throw new BadRequestException(
        'Both regions must have geographic coordinates for sea wars',
      );
    }

    // Calculate distance using Haversine formula
    const distance = calculateHaversineDistance(
      attackerRegion.latitude,
      attackerRegion.longitude,
      defenderRegion.latitude,
      defenderRegion.longitude,
    );

    // Distance must be within 1000km
    if (distance > 1000) {
      throw new BadRequestException(
        `Target region is too far away. Distance: ${distance.toFixed(1)}km, Maximum: 1000km`,
      );
    }
  }

  async getAvailableTargets(warType: WarType, attackingRegionId: string): Promise<AvailableTargetDto[]> {
    const attackingRegion = await this.regionRepository.findOne({
      where: { id: attackingRegionId },
      relations: ['state'],
    });

    if (!attackingRegion) {
      throw new NotFoundException('Attacking region not found');
    }

    if (warType === WarType.GROUND) {
      return this.getGroundWarTargets(attackingRegion);
    } else if (warType === WarType.SEA) {
      return this.getSeaWarTargets(attackingRegion);
    } else {
      throw new BadRequestException('Unsupported war type for target selection');
    }
  }

  private async getGroundWarTargets(attackingRegion: Region): Promise<AvailableTargetDto[]> {
    if (!attackingRegion.bordersWith || attackingRegion.bordersWith.length === 0) {
      return [];
    }

    const borderingRegions = await this.regionRepository.find({
      where: { countryCode: In(attackingRegion.bordersWith) },
      relations: ['state'],
    });

    // Filter out regions from the same state
    const validTargets = borderingRegions.filter(region => {
      // Allow targeting independent regions (no state)
      if (!region.state || !attackingRegion.state) {
        return true;
      }
      // Exclude regions from the same state
      return region.state.id !== attackingRegion.state.id;
    });

    return validTargets.map(region => ({
      id: region.id,
      name: region.name,
      countryCode: region.countryCode,
      seaAccess: region.seaAccess,
      population: region.population || 0,
      state: region.state ? {
        id: region.state.id,
        name: region.state.name,
      } : undefined,
    }));
  }

  private async getSeaWarTargets(attackingRegion: Region): Promise<AvailableTargetDto[]> {
    // Attacking region must have sea access
    if (!attackingRegion.seaAccess) {
      throw new BadRequestException('Attacking region must have sea access for sea wars');
    }

    // Attacking region must have coordinates
    if (!attackingRegion.latitude || !attackingRegion.longitude) {
      throw new BadRequestException('Attacking region must have geographic coordinates');
    }

    // Get all regions with sea access
    const seaAccessRegions = await this.regionRepository.find({
      where: { seaAccess: true },
      relations: ['state'],
    });

    const targets: AvailableTargetDto[] = [];

    for (const region of seaAccessRegions) {
      // Skip the attacking region itself
      if (region.id === attackingRegion.id) {
        continue;
      }

      // Skip regions without coordinates
      if (!region.latitude || !region.longitude) {
        continue;
      }

      // Calculate distance
      const distance = calculateHaversineDistance(
        attackingRegion.latitude,
        attackingRegion.longitude,
        region.latitude,
        region.longitude,
      );

      // Only include regions within 1000km and not from the same state
      if (distance <= 1000) {
        // Filter out regions from the same state
        const isSameState = attackingRegion.state && region.state &&
                           attackingRegion.state.id === region.state.id;

        if (!isSameState) {
          targets.push({
            id: region.id,
            name: region.name,
            countryCode: region.countryCode,
            distance: Math.round(distance * 10) / 10, // Round to 1 decimal place
            seaAccess: region.seaAccess,
            population: region.population || 0,
            state: region.state ? {
              id: region.state.id,
              name: region.state.name,
            } : undefined,
          });
        }
      }
    }

    // Sort by distance
    return targets.sort((a, b) => (a.distance || 0) - (b.distance || 0));
  }

  private async calculateDamageRequirement(
    defenderRegionId: string,
    warType?: WarType,
  ): Promise<number> {
    const defenderRegion = await this.regionRepository.findOneBy({
      id: defenderRegionId,
    });

    if (!defenderRegion) {
      return 10000; // Default value
    }

    // For revolution wars, calculate based on stationed players
    if (warType === WarType.REVOLUTION) {
      return await this.calculateRevolutionDamageRequirement(defenderRegionId);
    }

    // Basic calculation for other war types
    return (defenderRegion.developmentIndex || 1) * 1000;
  }

  private async sendWarDeclarationNotifications(war: War): Promise<void> {
    try {
      const attackerName = war.attackerRegion?.name || 'Unknown';
      const defenderName = war.defenderRegion?.name || 'Unknown';
      const title = 'War Declared!';
      const content = `${attackerName} has declared war on ${defenderName}. War type: ${war.warType}`;

      // Get affected users (simplified for MVP)
      const affectedUsers = await this.userRepository.find({
        where: [
          { region: { id: war.attackerRegion.id } },
          { region: { id: war.defenderRegion.id } },
        ],
      });

      await this.notificationService.createWarNotification(
        affectedUsers,
        NotificationType.WAR_DECLARED,
        title,
        content,
        war.id,
        true
      );
    } catch (error) {
      console.error('Failed to send war declaration notifications:', error);
    }
  }

  // Add this helper method to centralize the winner determination logic
  public determineWarWinner(war: War): 'attacker' | 'defender' | null {
    if (war.status !== WarStatus.ENDED) {
      return null;
    }

    return war.attackerGroundDamage >
      war.damageRequirement + war.defenderGroundDamage
      ? 'attacker'
      : 'defender';
  }

  /**
   * Helper method to check if a war is in an active phase
   */
  public isWarActive(war: War): boolean {
    return war.status === WarStatus.ACTIVE;
  }

  /**
   * Join existing pending revolution (called from declareWar method)
   */
  private async joinExistingRevolution(user: User, existingRevolution: War): Promise<War> {
    // Validate revolution participation requirements
    await this.validateRevolutionParticipation(user, existingRevolution);

    // Deduct 500 gold immediately
    user.gold -= 500;
    await this.userRepository.save(user);

    // Add user to revolution participants
    if (!existingRevolution.participants.attackers) {
      existingRevolution.participants.attackers = [];
    }

    existingRevolution.participants.attackers.push({
      userId: user.id,
      username: user.username,
      damage: 0, // No damage yet, just commitment
      energySpent: 0,
      joinedAt: new Date(),
    });

    // If this is the 3rd participant, start the revolution
    if (existingRevolution.participants.attackers.length === 3) {
      existingRevolution.status = WarStatus.ACTIVE;
      existingRevolution.startedAt = new Date();

      // Set lastRevolutionAt timestamp since revolution is actually starting
      const defenderRegion = await this.regionRepository.findOne({
        where: { id: existingRevolution.defenderRegion.id },
      });
      if (defenderRegion) {
        defenderRegion.lastRevolutionAt = new Date();
        await this.regionRepository.save(defenderRegion);
      }

      // Schedule war end exactly 24 hours from now
      const timeout = global.setTimeout(async () => {
        await this.endWar(existingRevolution.id);
      }, this.WAR_DURATION_MS);

      // Store the timeout reference
      this.schedulerRegistry.addTimeout(`war_end_${existingRevolution.id}`, timeout);

      await this.sendWarDeclarationNotifications(existingRevolution);

      console.log(`[Revolution] War ${existingRevolution.id} started with 3 participants`);
    }

    // Save and return the updated war
    const updatedWar = await this.warRepository.save(existingRevolution);

    console.log(`[Revolution] User ${user.id} joined revolution. ${3 - existingRevolution.participants.attackers.length} more needed.`);
    return updatedWar;
  }

  /**
   * Validate revolution participation requirements
   */
  private async validateRevolutionParticipation(user: User, war: War): Promise<void> {
    // Check if user is a citizen of the target region
    if (user.region?.id !== war.defenderRegion.id) {
      throw new BadRequestException(
        'Only citizens of the target region can participate in revolution wars',
      );
    }

    // Level requirement
    if (user.level < 5) {
      throw new BadRequestException(
        'You must be at least level 5 to participate in revolution wars',
      );
    }

    // Check if user has enough gold (500 gold cost per participant)
    if (user.gold < 500) {
      throw new BadRequestException(
        'You need 500 gold to participate in a revolution war',
      );
    }

    // Check if user has already participated in this revolution (prevent same user from being multiple participants)
    const allParticipants = [
      ...(war.participants?.attackers || []),
      ...(war.participants?.defenders || []),
    ];
    const hasParticipated = allParticipants.some(p => p.userId === user.id);
    if (hasParticipated) {
      throw new BadRequestException(
        'You have already participated in this revolution war. Each user can only participate once.',
      );
    }

    // Check if revolution already has 3 participants (maximum allowed)
    if (allParticipants.length >= 3) {
      throw new BadRequestException(
        'This revolution already has the maximum number of participants (3)',
      );
    }
  }

  async findWarsByUser(userId: number): Promise<War[]> {
    const wars = await this.warRepository.find({
      relations: {
        attackerRegion: true,
        defenderRegion: true,
        declaredBy: true,
      },
      order: {
        declaredAt: 'DESC', // Add this to sort in descending order
      },
    });

    // Filter wars where user is either a participant or the declarer
    return wars.filter((war) => {
      // Check if user is the declarer
      if (war.declaredBy?.id === userId) {
        return true;
      }

      // Check if user is a participant
      const isAttacker = war.participants?.attackers?.some(
        (participant) => participant.userId === userId,
      );
      const isDefender = war.participants?.defenders?.some(
        (participant) => participant.userId === userId,
      );

      return isAttacker || isDefender;
    });
  }

  async participateInWar(
    userId: number,
    warId: string,
    participateDto: ParticipateInWarDto,
  ): Promise<War> {
    // Get user without updating energy (to avoid double energy update)
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['region', 'region.state'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (user.isTraveling) {
      throw new BadRequestException(
        'You are already traveling, cannot participate in war!',
      );
    }

    // Get the war
    const war = await this.findWarById(warId);
    if (!war) {
      throw new NotFoundException(`War with ID ${warId} not found`);
    }

    if (!this.isWarActive(war)) {
      throw new BadRequestException(
        'Can only participate in war when it is active',
      );
    }

    // Determine which side the user is on based on war type
    let isAttacker: boolean;
    let isDefender: boolean;

    if (war.warType === WarType.REVOLUTION) {
      // For revolution wars, both sides are from the same region (defenderRegion)
      if (user.region?.id !== war.defenderRegion.id) {
        throw new BadRequestException(
          'You can only participate in revolution wars in your own region',
        );
      }

      // For revolution wars, user must specify which side they want to join
      if (!participateDto.side) {
        throw new BadRequestException(
          'You must specify which side you want to join in revolution wars (attacker or defender)',
        );
      }

      // For revolution wars, check if user is switching sides
      const existingAttacker = war.participants?.attackers?.find(p => p.userId === userId);
      const existingDefender = war.participants?.defenders?.find(p => p.userId === userId);
      const hasParticipated = existingAttacker || existingDefender;

      if (hasParticipated) {
        // Check if user is switching sides
        if (existingAttacker && participateDto.side === 'defender') {
          console.log(`[Revolution] User ${userId} is switching from attacker to defender`);

          // Add user to defender side with new timestamp (they'll keep their attacker record too)
          if (!war.participants.defenders) {
            war.participants.defenders = [];
          }

          // Check if user already exists on defender side
          const existingDefenderEntry = war.participants.defenders.find(p => p.userId === userId);
          if (!existingDefenderEntry) {
            war.participants.defenders.push({
              userId,
              username: user.username,
              damage: 0, // Start fresh on new side
              energySpent: 0,
              joinedAt: new Date(),
            });
          }

          // Update auto mode preferred side if user has active auto mode
          if (user.activeAutoMode === 'war' && user.autoTargetId === warId) {
            await this.updateAutoModePreferredSide(userId, warId, 'defender');
          }
        } else if (existingDefender && participateDto.side === 'attacker') {
          console.log(`[Revolution] User ${userId} is switching from defender to attacker`);

          // Add user to attacker side with new timestamp (they'll keep their defender record too)
          if (!war.participants.attackers) {
            war.participants.attackers = [];
          }

          // Check if user already exists on attacker side
          const existingAttackerEntry = war.participants.attackers.find(p => p.userId === userId);
          if (!existingAttackerEntry) {
            war.participants.attackers.push({
              userId,
              username: user.username,
              damage: 0, // Start fresh on new side
              energySpent: 0,
              joinedAt: new Date(),
            });
          }

          // Update auto mode preferred side if user has active auto mode
          if (user.activeAutoMode === 'war' && user.autoTargetId === warId) {
            await this.updateAutoModePreferredSide(userId, warId, 'attacker');
          }
        } else if (
          (existingAttacker && participateDto.side === 'attacker') ||
          (existingDefender && participateDto.side === 'defender')
        ) {
          // User is already on the requested side - this is fine, just continue with normal participation
          console.log(`[Revolution] User ${userId} is already on ${participateDto.side} side, continuing with attack`);
        }
      }

      // Save the war if user switched sides to persist the new participant entry
      if (hasParticipated && (
        (existingAttacker && participateDto.side === 'defender') ||
        (existingDefender && participateDto.side === 'attacker')
      )) {
        await this.warRepository.save(war);
      }

      isAttacker = participateDto.side === 'attacker';
      isDefender = participateDto.side === 'defender';
    } else {
      // For regular wars, determine side based on region
      isAttacker = user.region?.id === war.attackerRegion.id;
      isDefender = user.region?.id === war.defenderRegion.id;

      if (!isAttacker && !isDefender) {
        throw new BadRequestException(
          'You can only participate in wars involving your region',
        );
      }
    }

    // Calculate current energy without saving (to avoid double energy update)
    const currentEnergy = this.energyService.calculateCurrentEnergy(user);
    let energyToSpend = currentEnergy;

    // Handle auto mode
    if (participateDto.autoMode) {
      // Only premium users can use auto mode for initial setup
      if (!user.isPremium && energyToSpend === 0) {
        throw new BadRequestException(
          'Auto mode is only available for premium users',
        );
      }

      // Start auto attack - this will handle the initial attack and set up the cron job
      await this.warAutoService.startAutoAttack(
        userId,
        warId,
        participateDto.autoEnergyPercentage || 50, // Default to 50% if not specified
      );

      // For revolution wars, save the preferred side for auto mode after starting auto attack
      if (war.warType === WarType.REVOLUTION && participateDto.side) {
        // Access the war auto handler through the module ref to save preferred side
        try {
          const warAutoHandler = this.moduleRef.get('WarAutoHandler', { strict: false });
          if (warAutoHandler && typeof warAutoHandler.saveAutoSettings === 'function') {
            await warAutoHandler.saveAutoSettings(userId, warId, participateDto.side);
          }
        } catch (error) {
          this.logger.warn(`Could not save preferred side for revolution war auto mode: ${error.message}`);
        }
      }

      // Return the war without executing the manual attack logic
      // The auto attack system will handle the initial attack
      return this.findWarById(warId);
    }

    // Log the user's current energy
    console.log(
      `War participation - User ID: ${userId}, Current energy: ${currentEnergy}, Requested energy: ${energyToSpend}`,
    );

    // Ensure we don't try to use more energy than available
    const originalEnergyRequest = energyToSpend;
    energyToSpend = Math.min(energyToSpend, currentEnergy);

    // If energy was capped, log it
    if (energyToSpend < originalEnergyRequest) {
      console.log(
        `Energy request capped - Original: ${originalEnergyRequest}, Available: ${energyToSpend}`,
      );
    }

    // If no energy available, skip this attack
    if (energyToSpend <= 0) {
      throw new BadRequestException(
        `Insufficient energy to participate in war. Available: ${currentEnergy}, Required: ${originalEnergyRequest}`,
      );
    }

    // Log the final energy to spend
    console.log(
      `Final energy to spend - User ID: ${userId}, Energy: ${energyToSpend}`,
    );

    // Use a transaction to handle energy consumption and user updates atomically
    const updatedUser = await this.userRepository.manager.transaction(
      async (transactionManager) => {
        // Get user with pessimistic locking
        const lockedUser = await transactionManager.findOne(User, {
          where: { id: userId },
          lock: { mode: 'pessimistic_write' },
        });

        if (!lockedUser) {
          throw new NotFoundException('User not found');
        }

        // Update energy within the transaction
        lockedUser.updateEnergy();

        // Check if user has enough energy
        if (lockedUser.energy < energyToSpend) {
          throw new BadRequestException(
            `Insufficient energy: User has ${lockedUser.energy} but needs ${energyToSpend}`,
          );
        }

        // Log energy consumption for debugging
        console.log(
          `Before consumption - User ID: ${userId}, Energy: ${lockedUser.energy}, Amount to consume: ${energyToSpend}`,
        );

        // Consume energy directly
        lockedUser.energy -= energyToSpend;

        // Log energy after consumption
        console.log(
          `After consumption - User ID: ${userId}, Remaining energy: ${lockedUser.energy}`,
        );

        // Grant XP for fighting (2 XP per energy spent)
        lockedUser.experience += energyToSpend * 2;
        lockedUser.checkAndUpdateLevel();

        // Save and return the updated user
        return await transactionManager.save(User, lockedUser);
      },
    );

    // Calculate damage based on energy spent and user attributes
    const damage = this.calculateDamage(energyToSpend, updatedUser);

    // Update war with new damage
    if (isAttacker) {
      war.attackerGroundDamage += damage;
      if (!war.participants.attackers) {
        war.participants.attackers = [];
      }

      // Find existing participant on attacker side
      const existingAttacker = war.participants.attackers.find(p => p.userId === userId);
      if (existingAttacker) {
        // Update existing participant's damage and energy
        existingAttacker.damage += damage;
        existingAttacker.energySpent += energyToSpend;
      } else {
        // Add new participant entry for attacker side
        war.participants.attackers.push({
          userId,
          username: user.username,
          damage,
          energySpent: energyToSpend,
          joinedAt: new Date(),
        });
      }
    } else {
      war.defenderGroundDamage += damage;
      if (!war.participants.defenders) {
        war.participants.defenders = [];
      }

      // Find existing participant on defender side
      const existingDefender = war.participants.defenders.find(p => p.userId === userId);
      if (existingDefender) {
        // Update existing participant's damage and energy
        existingDefender.damage += damage;
        existingDefender.energySpent += energyToSpend;
      } else {
        // Add new participant entry for defender side
        war.participants.defenders.push({
          userId,
          username: user.username,
          damage,
          energySpent: energyToSpend,
          joinedAt: new Date(),
        });
      }
    }

    // Save and return updated war
    return this.warRepository.save(war);
  }

  private calculateDamage(energyAmount: number, user: User): number {
    // Enhanced damage calculation including user attributes
    // Based on the formula: Damage = (1 + Strength/100 + Level/200) * Energy * 100

    // Extract user attributes
    const strength = user.strength || 10; // Default to 10 if not set
    const level = user.level || 1; // Default to 1 if not set

    // Calculate the multiplier
    const multiplier = 1 + strength / 100 + level / 200;

    // Calculate and return the damage
    return Math.floor(energyAmount * 100 * multiplier);
  }

  /**
   * Validate revolution war requirements
   */
  private async validateRevolutionWar(
    user: User,
    defenderRegion: Region,
  ): Promise<void> {
    // Check if user is a citizen of the target region
    if (user.region?.id !== defenderRegion.id) {
      throw new BadRequestException(
        'You can only start a revolution in your own region',
      );
    }

    // Check if user is level 5 or higher
    if (user.level < 5) {
      throw new BadRequestException(
        'You must be at least level 5 to start a revolution',
      );
    }

    // Check revolution cooldown (4 days) - ONLY during declaration
    if (defenderRegion.lastRevolutionAt) {
      const fourDaysAgo = new Date(Date.now() - 4 * 24 * 60 * 60 * 1000);
      if (defenderRegion.lastRevolutionAt > fourDaysAgo) {
        throw new BadRequestException(
          'A revolution can only be started once every 4 days in this region',
        );
      }
    }
  }

  /**
   * Calculate damage requirement for revolution wars based on stationed players
   */
  private async calculateRevolutionDamageRequirement(
    defenderRegionId: string,
  ): Promise<number> {
    // Get all users stationed in the target region
    const stationedUsers = await this.userRepository.find({
      where: { region: { id: defenderRegionId } },
    });
    let totalDefense = 0;

    for (const user of stationedUsers) {
      const levelFactor = (user.level || 1) * 50;
      // PlayerPower = (STR × 600) + (INT × 400) + (END × 200)
      const playerPower =
        (user.strength || 10) * 600 +
        (user.intelligence || 10) * 400 +
        (user.endurance || 10) * 200 +
        levelFactor;

      totalDefense += playerPower;
    }
    
    // Minimum damage requirement to prevent too easy revolutions
    return Math.max(totalDefense, 1000);
  }

  /**
   * Process a successful revolution victory
   */
  private async processRevolutionVictory(war: War): Promise<void> {
    try {
      // Load the target region with its current state relationship
      const targetRegion = await this.regionRepository.findOne({
        where: { id: war.defenderRegion.id },
        relations: ['state'],
      });

      if (!targetRegion) {
        console.error(`[processRevolutionVictory] Target region not found: ${war.defenderRegion.id}`);
        return;
      }

      const originalState = targetRegion.state;
      console.log(`[processRevolutionVictory] Original state: ${originalState?.name || 'Independent'}`);

      // Create a new temporary state with open borders
      const newStateName = `${targetRegion.name} State`;
      const newState = await this.stateService.createRevolutionState(
        newStateName,
        targetRegion.id,
      );

      console.log(`[processRevolutionVictory] Created new state: ${newState.name} (ID: ${newState.id})`);

      // If the region was part of another state, remove it first
      if (originalState) {
        try {
          await this.stateService.removeRegion(originalState.id, targetRegion.id);
          console.log(`[processRevolutionVictory] Removed region from original state: ${originalState.name}`);
        } catch (error) {
          console.error(`[processRevolutionVictory] Error removing region from original state: ${error.message}`);
        }
      }

      // Transfer the region to the new state using the state service
      try {
        await this.stateService.addRegion(newState.id, { regionId: targetRegion.id });
        console.log(`[processRevolutionVictory] Added region to new state: ${newState.name}`);
      } catch (error) {
        console.error(`[processRevolutionVictory] Error adding region to new state: ${error.message}`);

        // Fallback: direct database update
        targetRegion.state = newState;
        targetRegion.status = RegionStatus.STATE_MEMBER;
        await this.regionRepository.save(targetRegion);
        console.log(`[processRevolutionVictory] Fallback: Direct region update completed`);
      }

      // Verify the transfer was successful
      const updatedRegion = await this.regionRepository.findOne({
        where: { id: targetRegion.id },
        relations: ['state'],
      });

      if (updatedRegion?.state?.id === newState.id) {
        console.log(`[processRevolutionVictory] Region transfer verified successfully`);
      } else {
        console.error(`[processRevolutionVictory] Region transfer verification failed`);
      }

      // Trigger immediate elections for the new state
      try {
        await this.stateElectionService.scheduleElection(newState.id);
        console.log(`[processRevolutionVictory] Elections scheduled for new state`);
      } catch (error) {
        console.error('Failed to trigger elections for revolution state:', error);
      }

      console.log(
        `[processRevolutionVictory] Revolution successful! Created new state: ${newStateName}`,
      );
    } catch (error) {
      console.error('Error processing revolution victory:', error);
    }
  }

  /**
   * Stop auto attack mode for a user in a specific war
   */
  async stopAutoAttack(userId: number, warId: string): Promise<void> {
    // Verify the war exists
    const war = await this.findWarById(warId);
    if (!war) {
      throw new NotFoundException(`War with ID ${warId} not found`);
    }

    // Verify the user exists
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['region'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if user has active auto mode for this war
    if (user.activeAutoMode !== 'war') {
      throw new BadRequestException('No active auto attack found');
    }

    // Stop the auto attack
    await this.warAutoService.stopAutoAttack(userId, user.autoTargetId);
  }

  async endWar(warId: string): Promise<War> {
    const war = await this.findWarById(warId);

    if (war.status === WarStatus.ENDED) {
      throw new BadRequestException('War is already ended');
    }

    // Update war status to ENDED
    war.status = WarStatus.ENDED;
    war.endedAt = new Date();

    // Stop auto attacks for all participants
    const allParticipants = [
      ...(war.participants?.attackers || []),
      ...(war.participants?.defenders || []),
    ];

    // Stop auto attacks for each participant
    for (const participant of allParticipants) {
      this.warAutoService.stopAutoAttack(participant.userId, warId);
    }

    // Clean up the timeout
    try {
      this.schedulerRegistry.deleteTimeout(`war_end_${warId}`);
    } catch (error) {
      console.log(`[endWar] Error clearing timeout: ${error.message}`);
    }

    // Save the war with updated status
    const savedWar = await this.warRepository.save(war);

    // Process war outcome
    await this.processWarOutcome(savedWar);

    return savedWar;
  }

  /**
   * Process the outcome of a war, including region transfers for conquest wars
   */
  private async processWarOutcome(war: War): Promise<void> {
    // Determine the winner
    const winner = this.determineWarWinner(war);
    if (!winner) {
      return;
    }

    // Handle revolution wars
    if (war.warType === WarType.REVOLUTION && winner === 'attacker') {
      await this.processRevolutionVictory(war);
      await this.sendWarEndNotifications(war, winner);
      return;
    }

    // Handle sea wars - if attackers win, automatically trigger ground war
    if (war.warType === WarType.SEA && winner === 'attacker') {
      await this.triggerGroundWarAfterSeaVictory(war);
      await this.sendWarEndNotifications(war, winner);
      return;
    }

    // For conquest wars, use defenderRegion as targetRegion if targetRegion is not set
    if (
      war.warTarget === WarTarget.CONQUEST &&
      !war.targetRegion &&
      war.defenderRegion
    ) {
      console.log('[processWarOutcome] Setting defenderRegion as targetRegion');
      war.targetRegion = war.defenderRegion;
    }

    if (
      winner === 'attacker' &&
      war.warTarget === WarTarget.CONQUEST &&
      war.targetRegion
    ) {
      await this.transferRegion(war);
    }

    // Send notifications about the war outcome
    await this.sendWarEndNotifications(war, winner);
  }

  /**
   * Trigger a ground war automatically after a successful sea war
   */
  private async triggerGroundWarAfterSeaVictory(seaWar: War): Promise<void> {
    try {
      console.log(`[triggerGroundWarAfterSeaVictory] Sea war ${seaWar.id} won by attackers, triggering ground war`);

      // Create a ground war with the same regions and target
      const groundWarDto = {
        warType: WarType.GROUND,
        warTarget: seaWar.warTarget,
        declaration: `Automatic ground war following sea victory in ${seaWar.defenderRegion.name}`,
        attackerRegionId: seaWar.attackerRegion.id,
        defenderRegionId: seaWar.defenderRegion.id,
      };

      // Get the original declarer to maintain authority
      const declarer = await this.userRepository.findOne({
        where: { id: seaWar.declaredBy.id },
        relations: ['region', 'region.state'],
      });

      if (!declarer) {
        console.error('[triggerGroundWarAfterSeaVictory] Original war declarer not found');
        return;
      }

      // Create the ground war directly without validation (since sea war already succeeded)
      const groundWar = await this.createGroundWarAfterSeaVictory(groundWarDto, declarer);

      console.log(`[triggerGroundWarAfterSeaVictory] Ground war ${groundWar.id} created successfully`);
    } catch (error) {
      console.error(`[triggerGroundWarAfterSeaVictory] Error creating ground war: ${error.message}`);
    }
  }

  /**
   * Create a ground war after sea victory without normal validations
   */
  private async createGroundWarAfterSeaVictory(createWarDto: any, declarer: User): Promise<War> {
    const [attackerRegion, defenderRegion] = await Promise.all([
      this.regionRepository.findOneBy({ id: createWarDto.attackerRegionId }),
      this.regionRepository.findOneBy({ id: createWarDto.defenderRegionId }),
    ]);

    if (!attackerRegion || !defenderRegion) {
      throw new Error('Regions not found for ground war creation');
    }

    // Calculate damage requirement
    const damageRequirement = await this.calculateDamageRequirement(
      createWarDto.defenderRegionId,
      WarType.GROUND,
    );

    // Create the ground war entity
    const newWar = this.warRepository.create({
      warType: WarType.GROUND,
      warTarget: createWarDto.warTarget,
      status: WarStatus.ACTIVE,
      declaration: createWarDto.declaration,
      attackerRegion,
      defenderRegion,
      declaredBy: declarer,
      declaredAt: new Date(),
      startedAt: new Date(),
      damageRequirement,
      attackerGroundDamage: 0,
      defenderGroundDamage: 0,
      participants: {
        attackers: [],
        defenders: [],
      },
      battleEvents: [],
    });

    const savedWar = await this.warRepository.save(newWar);

    // Schedule war end exactly 24 hours from now
    const timeout = global.setTimeout(async () => {
      await this.endWar(savedWar.id);
    }, this.WAR_DURATION_MS);

    // Store the timeout reference
    this.schedulerRegistry.addTimeout(`war_end_${savedWar.id}`, timeout);

    return savedWar;
  }

  /**
   * Transfer a region from one state to another after a successful conquest
   */
  private async transferRegion(war: War): Promise<void> {
    try {
      // Load the full region with state relation
      const targetRegion = await this.regionRepository.findOne({
        where: { id: war.targetRegion.id },
        relations: ['state'],
      });

      if (!targetRegion) {
        console.error(
          `[transferRegion] Target region with ID ${war.targetRegion.id} not found`,
        );
        return;
      }

      // Load the attacker state
      const attackerRegion = await this.regionRepository.findOne({
        where: { id: war.attackerRegion.id },
        relations: ['state'],
      });

      if (!attackerRegion?.state) {
        return;
      }
      console.log(
        `[transferRegion] Attacker state found: ${attackerRegion.state.name}`,
      );

      // If the region belongs to a state, remove it from that state
      if (targetRegion.state) {
        const defenderStateId = targetRegion.state.id;
        const defenderStateName = targetRegion.state.name;
        console.log(
          `[transferRegion] Removing region from current state: ${defenderStateId}`,
        );

        // Check if this is the last region in the state
        const defenderState = await this.stateService.findById(defenderStateId);

        const isLastRegion = defenderState?.regions?.length === 1;

        // First, remove the region from the state
        try {
          await this.stateService.removeRegion(
            defenderStateId,
            targetRegion.id,
          );

          // If this was the last region, delete the state
          if (isLastRegion && defenderState) {
            try {
              await this.stateService.removeState(defenderStateId);

              // Notify the state leader
              if (defenderState.leader) {
                await this.notificationService.createNotification(
                  defenderState.leader,
                  NotificationType.REGION_CONQUERED,
                  'State Dissolved',
                  `Your state ${defenderStateName} has been dissolved as it lost its last region.`,
                  war.id,
                  'war',
                  true,
                );
              }
            } catch (error) {
              console.error(
                `[transferRegion] Error removing state: ${error.message}`,
              );
            }
          }
        } catch (error) {
          console.error(
            `[transferRegion] Error removing region using stateService: ${error.message}`,
          );

          // Fallback: manually update the region
          targetRegion.state = null;
          targetRegion.status = RegionStatus.INDEPENDENT;
          await this.regionRepository.save(targetRegion);

          // If this was the last region, delete the state
          if (isLastRegion) {
            try {
              await this.stateService.removeState(defenderStateId);
            } catch (error) {
              console.error(
                `[transferRegion] Error deleting state manually: ${error.message}`,
              );
            }
          }
        }

        // Verify the region is now independent
        const updatedRegion = await this.regionRepository.findOne({
          where: { id: targetRegion.id },
          relations: ['state'],
        });
      }

      await this.stateService.addRegion(attackerRegion.state.id, {
        regionId: targetRegion.id,
      });

      // Notify users in the conquered region
      const usersInRegion = await this.userRepository.find({
        where: { region: { id: targetRegion.id } },
      });

      const attackerStateName = attackerRegion.state.name || 'Unknown';
      const regionName = targetRegion.name || 'Unknown';

      for (const user of usersInRegion) {
        await this.notificationService.createNotification(
          user,
          NotificationType.REGION_CONQUERED,
          'Region Conquered',
          `Your region ${regionName} has been conquered by ${attackerStateName}.`,
          war.id,
          'war',
          true,
        );
      }
    } catch (error) {
      console.error('Error transferring region:', error);
    }
  }

  /**
   * Send notifications about the end of a war
   */
  private async sendWarEndNotifications(
    war: War,
    winner: 'attacker' | 'defender',
  ): Promise<void> {
    try {
      const attackerName =
        war.attackerState?.name || war.attackerRegion?.name || 'Unknown';
      const defenderName =
        war.defenderState?.name || war.defenderRegion?.name || 'Unknown';

      const title = 'War Ended';
      let content = '';

      // Handle revolution wars differently since they happen within the same region
      if (war.warType === WarType.REVOLUTION) {
        const regionName = war.defenderRegion?.name || 'Unknown Region';
        if (winner === 'attacker') {
          content = `The revolution in ${regionName} has succeeded! The revolutionaries have overthrown the current leadership.`;
        } else {
          content = `The revolution in ${regionName} has been suppressed. The current leadership has successfully defended against the uprising.`;
        }
      } else {
        // Handle regular wars (conquest, resources, etc.)
        if (winner === 'attacker') {
          content = `${attackerName} has emerged victorious in the war against ${defenderName}.`;
          if (war.warTarget === WarTarget.CONQUEST && war.targetRegion) {
            content += ` ${war.targetRegion.name} has been conquered.`;
          }
        } else {
          content = `${defenderName} has successfully defended against ${attackerName}'s attack.`;
        }
      }

      // Get affected users
      const affectedUsers = await this.userRepository.find({
        where: [
          { region: { id: war.attackerRegion.id } },
          { region: { id: war.defenderRegion.id } },
        ],
      });

      // Add users from the target region if it's different from attacker/defender regions
      if (
        war.targetRegion &&
        war.targetRegion.id !== war.attackerRegion.id &&
        war.targetRegion.id !== war.defenderRegion.id
      ) {
        const targetRegionUsers = await this.userRepository.find({
          where: { region: { id: war.targetRegion.id } },
        });
        affectedUsers.push(...targetRegionUsers);
      }

      // Send notifications to all affected users
      for (const user of affectedUsers) {
        await this.notificationService.createNotification(
          user,
          NotificationType.WAR_ENDED,
          title,
          content,
          war.id,
          'war',
          true, // Send email for important notifications
        );
      }
    } catch (error) {
      console.error('Failed to send war end notifications:', error);
    }
  }

  /**
   * Simulate a war outcome for testing purposes
   */
  async simulateWarOutcome(warId: string, winner: 'attacker' | 'defender'): Promise<War> {
    const war = await this.findWarById(warId);

    if (war.status === WarStatus.ENDED) {
      throw new BadRequestException('War is already ended');
    }

    if (!['attacker', 'defender'].includes(winner)) {
      throw new BadRequestException('Winner must be either "attacker" or "defender"');
    }

    console.log(`[simulateWarOutcome] Simulating ${winner} victory for war ${warId} (type: ${war.warType})`);

    // Set damage values to simulate the desired outcome
    if (winner === 'attacker') {
      // Attacker wins: set attacker damage higher than requirement + defender damage
      war.attackerGroundDamage = war.damageRequirement + 1000;
      war.defenderGroundDamage = 500;
    } else {
      // Defender wins: set attacker damage lower than requirement + defender damage
      war.attackerGroundDamage = war.damageRequirement - 1000;
      war.defenderGroundDamage = 2000;
    }

    // Add some fake battle events to make it look realistic
    const battleEvent = {
      timestamp: new Date(),
      description: `Simulated ${winner} victory for testing`,
      damage: winner === 'attacker' ? war.attackerGroundDamage : war.defenderGroundDamage,
      side: winner as 'attacker' | 'defender',
      userId: war.declaredBy.id,
    };

    if (!war.battleEvents) {
      war.battleEvents = [];
    }
    war.battleEvents.push(battleEvent);

    // Save the war with simulated damage
    await this.warRepository.save(war);

    // Now end the war normally - this will trigger the outcome processing
    return this.endWar(warId);
  }

  async onApplicationBootstrap() {
    await this.restoreWarEndSchedules();
  }

  async restoreWarEndSchedules() {
    const activeWars = await this.warRepository.find({
      where: [
        { status: WarStatus.PENDING },
        { status: WarStatus.ACTIVE },
      ],
    });

    for (const war of activeWars) {
      if (!war.startedAt) continue;

      const now = new Date().getTime();
      const warEndTime = war.startedAt.getTime() + this.WAR_DURATION_MS;
      const remainingTime = warEndTime - now;

      if (remainingTime <= 0) {
        // War should have ended already
        await this.endWar(war.id);
      } else {
        // Reschedule the remaining time
        const timeout = global.setTimeout(async () => {
          await this.endWar(war.id);
        }, remainingTime);

        this.schedulerRegistry.addTimeout(`war_end_${war.id}`, timeout);
      }
    }
  }

  /**
   * Update the preferred side for auto mode when user switches sides in revolution wars
   */
  private async updateAutoModePreferredSide(
    userId: number,
    warId: string,
    newSide: 'attacker' | 'defender',
  ): Promise<void> {
    try {
      const warAutoHandler = this.moduleRef.get('WarAutoHandler', { strict: false });
      if (warAutoHandler && typeof warAutoHandler.saveAutoSettings === 'function') {
        await warAutoHandler.saveAutoSettings(userId, warId, newSide);
        this.logger.log(`Updated auto mode preferred side for user ${userId} in war ${warId} to ${newSide}`);
      }
    } catch (error) {
      this.logger.warn(`Could not update auto mode preferred side: ${error.message}`);
    }
  }

  /**
   * Get user's current side in a revolution war based on their most recent participation
   */
  async getUserSideInRevolution(userId: number, warId: string): Promise<{ side: 'attacker' | 'defender' | null }> {
    const war = await this.findWarById(warId);

    if (!war) {
      throw new NotFoundException(`War with ID ${warId} not found`);
    }

    if (war.warType !== 'revolution') {
      throw new BadRequestException('This endpoint is only for revolution wars');
    }

    // Find user in both attacker and defender lists
    const attackerParticipant = war.participants?.attackers?.find(p => p.userId === userId);
    const defenderParticipant = war.participants?.defenders?.find(p => p.userId === userId);

    // If user is not in either list, return null
    if (!attackerParticipant && !defenderParticipant) {
      return { side: null };
    }

    // If user is only in one list, return that side
    if (attackerParticipant && !defenderParticipant) {
      return { side: 'attacker' };
    }

    if (defenderParticipant && !attackerParticipant) {
      return { side: 'defender' };
    }

    // If user is in both lists (switched sides), determine current side based on most recent joinedAt timestamp
    if (attackerParticipant && defenderParticipant) {
      const attackerJoinedAt = new Date(attackerParticipant.joinedAt);
      const defenderJoinedAt = new Date(defenderParticipant.joinedAt);

      // Return the side with the most recent joinedAt timestamp
      return { side: attackerJoinedAt > defenderJoinedAt ? 'attacker' : 'defender' };
    }

    return { side: null };
  }
}
